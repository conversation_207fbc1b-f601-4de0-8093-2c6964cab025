package cn.sdtbu.edu.controller;

import cn.sdtbu.edu.dto.PlaylistDTO;
import cn.sdtbu.edu.service.PlaylistService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 歌单控制器测试类
 * <AUTHOR>
 */
@WebMvcTest(PlaylistController.class)
public class PlaylistControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private PlaylistService playlistService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    public void testGetPublicPlaylists_Success() throws Exception {
        // 准备测试数据
        PlaylistDTO playlist1 = new PlaylistDTO();
        playlist1.setId(1);
        playlist1.setUserId(123);
        playlist1.setName("治愈心灵的音乐");
        playlist1.setDescription("精选治愈系音乐，帮助放松心情");
        playlist1.setIsPublic(true);
        playlist1.setItemCount(15);
        playlist1.setUserName("音乐爱好者");
        playlist1.setUserAvatar("/img/avatar01.png");
        playlist1.setCreatedAt(new Timestamp(System.currentTimeMillis()));
        playlist1.setUpdatedAt(new Timestamp(System.currentTimeMillis()));

        PlaylistDTO playlist2 = new PlaylistDTO();
        playlist2.setId(2);
        playlist2.setUserId(456);
        playlist2.setName("深度冥想合集");
        playlist2.setDescription("专业冥想引导音频合集");
        playlist2.setIsPublic(true);
        playlist2.setItemCount(8);
        playlist2.setUserName("冥想导师");
        playlist2.setUserAvatar("/img/avatar02.png");
        playlist2.setCreatedAt(new Timestamp(System.currentTimeMillis()));
        playlist2.setUpdatedAt(new Timestamp(System.currentTimeMillis()));

        List<PlaylistDTO> mockPlaylists = Arrays.asList(playlist1, playlist2);

        // 模拟服务层返回
        when(playlistService.getPublicPlaylists()).thenReturn(mockPlaylists);

        // 执行请求并验证结果
        mockMvc.perform(get("/api/playlists/public"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("获取公开歌单列表成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].id").value(1))
                .andExpect(jsonPath("$.data[0].name").value("治愈心灵的音乐"))
                .andExpect(jsonPath("$.data[0].userName").value("音乐爱好者"))
                .andExpect(jsonPath("$.data[0].itemCount").value(15))
                .andExpect(jsonPath("$.data[1].id").value(2))
                .andExpect(jsonPath("$.data[1].name").value("深度冥想合集"))
                .andExpect(jsonPath("$.data[1].userName").value("冥想导师"))
                .andExpect(jsonPath("$.data[1].itemCount").value(8));
    }

    @Test
    public void testGetPublicPlaylists_WithLimit_Success() throws Exception {
        // 准备测试数据
        PlaylistDTO playlist1 = new PlaylistDTO();
        playlist1.setId(1);
        playlist1.setUserId(123);
        playlist1.setName("治愈心灵的音乐");
        playlist1.setDescription("精选治愈系音乐，帮助放松心情");
        playlist1.setIsPublic(true);
        playlist1.setItemCount(15);
        playlist1.setUserName("音乐爱好者");
        playlist1.setUserAvatar("/img/avatar01.png");
        playlist1.setCreatedAt(new Timestamp(System.currentTimeMillis()));
        playlist1.setUpdatedAt(new Timestamp(System.currentTimeMillis()));

        List<PlaylistDTO> mockPlaylists = Arrays.asList(playlist1);

        // 模拟服务层返回
        when(playlistService.getPublicPlaylists(1)).thenReturn(mockPlaylists);

        // 执行请求并验证结果
        mockMvc.perform(get("/api/playlists/public?limit=1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("获取公开歌单列表成功"))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(1))
                .andExpect(jsonPath("$.data[0].id").value(1))
                .andExpect(jsonPath("$.data[0].name").value("治愈心灵的音乐"));
    }

    @Test
    public void testGetPublicPlaylists_InvalidLimit_BadRequest() throws Exception {
        // 测试无效的limit参数
        mockMvc.perform(get("/api/playlists/public?limit=0"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("限制数量必须大于0"));

        mockMvc.perform(get("/api/playlists/public?limit=101"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(400))
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("限制数量不能超过100"));
    }

    @Test
    public void testGetPublicPlaylists_ServiceException_Error() throws Exception {
        // 模拟服务层抛出异常
        when(playlistService.getPublicPlaylists()).thenThrow(new RuntimeException("数据库连接失败"));

        // 执行请求并验证结果
        mockMvc.perform(get("/api/playlists/public"))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.code").value(500))
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("获取公开歌单列表失败: 数据库连接失败"));
    }
}
